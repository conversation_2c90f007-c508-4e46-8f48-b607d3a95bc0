#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const copyFile = promisify(fs.copyFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const mkdir = promisify(fs.mkdir);

const DIST_DIR = './dist';
const TARGET_DIR = '../mpClient/src/test/remote';

// 递归复制目录
async function copyDirectory(src, dest) {
  try {
    // 确保目标目录存在
    await mkdir(dest, { recursive: true });
    
    const entries = await readdir(src);
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry);
      const destPath = path.join(dest, entry);
      
      const entryStat = await stat(srcPath);
      
      if (entryStat.isDirectory()) {
        await copyDirectory(srcPath, destPath);
      } else {
        await copyFile(srcPath, destPath);
        console.log(`已复制: ${srcPath} -> ${destPath}`);
      }
    }
  } catch (error) {
    console.error('复制文件时出错:', error);
  }
}

// 移动 dist 目录下的所有文件到目标目录
async function moveBuildOutput() {
  try {
    if (fs.existsSync(DIST_DIR)) {
      console.log('开始移动构建产物...');
      await copyDirectory(DIST_DIR, TARGET_DIR);
      console.log('构建产物移动完成!');
    } else {
      console.log('dist 目录不存在，跳过移动操作');
    }
  } catch (error) {
    console.error('移动构建产物时出错:', error);
  }
}

// 监听 dist 目录变化
function watchDistDirectory() {
  if (!fs.existsSync(DIST_DIR)) {
    console.log('等待 dist 目录创建...');
    // 如果 dist 目录不存在，等待它被创建
    const checkInterval = setInterval(() => {
      if (fs.existsSync(DIST_DIR)) {
        clearInterval(checkInterval);
        console.log('检测到 dist 目录，开始监听...');
        startWatching();
      }
    }, 1000);
  } else {
    startWatching();
  }
}

function startWatching() {
  let timeout;
  
  fs.watch(DIST_DIR, { recursive: true }, (eventType, filename) => {
    if (filename) {
      console.log(`检测到文件变化: ${filename}`);
      
      // 使用防抖，避免频繁触发
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        moveBuildOutput();
      }, 500);
    }
  });
  
  console.log('正在监听 dist 目录变化...');
}

// 启动 vite build --watch
console.log('启动 Vite 构建 (watch 模式)...');
const viteProcess = spawn('npx', ['vite', 'build', '--watch', '--mode', 'development'], {
  stdio: 'inherit',
  shell: true
});

// 监听 dist 目录变化
watchDistDirectory();

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n正在停止构建进程...');
  viteProcess.kill('SIGINT');
  process.exit(0);
});

viteProcess.on('close', (code) => {
  console.log(`Vite 进程退出，代码: ${code}`);
  process.exit(code);
});
