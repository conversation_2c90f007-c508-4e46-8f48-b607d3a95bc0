<template>
  <div class="c-b-s">
    <!--    <button @click="exec" class="" :disabled="props.data.loading"-->
    <!--            :class="props.data.loading ? 'loading' : (props.data.active ? 'active' : '')">-->
    <!--      {{ props.data.name }}-->
    <!--    </button>-->

    <n-button @click="exec" :loading="props.data.loading" strong :secondary="!props.data.active"
      :type="props.data.active ? 'success' : 'error'" size="small">
      {{ props.data.name }}
    </n-button>
  </div>
</template>
<script setup lang="ts">
import { onMounted } from "vue";
import type { FunctionType } from "@/core/z";
import { NSpace, NButton, useOsTheme, NInput } from "naive-ui";
import ButtonGroup from "./button-group/index.vue";
import { useGameStore } from "@/stores/game";
import AutoDoGem from "@/components/auto-do-gem.vue";
import AutoUseItem from "@/components/auto-use-item.vue";
import ClearBagArg from "@/components/clear-bag-arg.vue";
import KillBoss from "@/components/kill-boss.vue";
import Evt from "@/core/evt";
import Handler from "@/core/Handler";
import AutoIdentify from "@/components/auto-identify.vue";
import DailyTaskSelect from "@/components/daily-task-select.vue";
import FullScreenTip from "@/components/full-screen-tip.vue";
import CustomerSelect from "@/components/customer-select.vue";

const osTheme = useOsTheme();
const game = useGameStore()

interface Props {
  data: FunctionType,
  index: number
}

const props = withDefaults(defineProps<Props>(), { data: {} as FunctionType, index: 0 })

onMounted(() => {
  if (props.data.name == "地图工具") {
    const EVT_OPEN_MAP_TOOL = Handler.alloc(null, () => {
      exec()
    })
    // 地图工具
    Evt.on("EVT_OPEN_MAP_TOOL", EVT_OPEN_MAP_TOOL, true)
  }
})

// 监听主题变化
watch(
  osTheme,
  newValue => {
    const isDark = newValue === 'dark';

  },
  { immediate: true }
);

const exec = () => {
  // 需要进入游戏后才能使用的功能
  if (props.data.inGame) {
    if (window._world && !window._world.isEnterGame()) {
      return void window.$message.error("请先进入游戏")
    }
  }
  // 地图工具
  if (props.data.name == "地图工具") {
    return showMapTool()
  }
  // 宝石工具
  if (props.data.name == "宝石工具") {
    return showGemTool()
  }
  // 鉴定工具
  if (props.data.name == "鉴定工具") {
    return showIdentifyTool()
  }
  if (props.data.name == "使用道具") {
    return showUseItemTool()
  }
  if (props.data.name == "清理参数") {
    return showClearBagArg()
  }
  const regex = new RegExp(
    /^https:\/\/worldh5\.gamehz\.cn\/version\/world\/publish\/channel\/res\/index\.html\?xameid=1000&xhannel=10800&mbUserId=[^&]+&mbToken=[^&]+&mbGameId=33985$/
  );
  if (props.data.name == "天宇上号") {
    const urlRef = ref("")
    const logic = () => {
      const cur = parseGameUrl().trim()
      if (urlRef.value == cur) {
        return void window.$message.error("输入的链接不可以和当前链接一致！")
      }
      if (!urlRef.value) {
        return void window.$message.error("请输入正确的天宇游戏链接！")
      }
      if (!regex.test(urlRef.value)) {
        return void window.$message.error("请输入正确的天宇游戏链接！")
      }
      const gameId = (window as any).GAME_ID || ""
      const toUrl = urlRef.value
      window.DASHBOARD_EXEC && window.DASHBOARD_EXEC(`
      window['RS${gameId}'] && clearInterval(window['RS${gameId}']);
      window['RS${gameId}'] = setInterval(() => {
        try{
          const exec = window.GAME_EXEC || null;
          if(!exec) return;
          if(!exec('${gameId}', "typeof tyybase != undefined && tyybase.is_init")) return;
          clearInterval(window['RS${gameId}']);
          exec('${gameId}', 'const frame = document.createElement("iframe");frame.style.display = "none";frame.src = "${toUrl}";document.body.prepend(frame);');
        }catch(e){
          clearInterval(window['RS${gameId}']);
        }
      }, 100);
    `)
      const reload = (window as any).RELOAD_CALL_BACK
      reload && reload()
    }
    const dlg = window.$dialog.info({
      title: props.data.name,
      maskClosable: false,
      closable: false,
      content: () => h(NSpace, { style: { marginTop: "4px" } },
        {
          default: () => h(NInput, {
            value: urlRef.value,
            placeholder: "游戏链接会替换现有书签链接",
            size: "tiny",
            style: {
              width: '240px',
            },
            onInput: (e) => {
              urlRef.value = e
            }
          }, {})
        }),
      action: () => h(ButtonGroup, {
        action: [
          { label: "确定", type: "error", callback: () => logic() },
          { label: "关闭", type: "default", callback: () => dlg.destroy() },
        ]
      })
    })


    return
  }
  if (props.data.name == "天宇链接") {
    window.copy(parseGameUrl())
    window.$message.success("获取天宇游戏链接成功,请注意账号安全。")
    return
  }
  // 分组按钮，检查冲突
  if (!props.data.active && props.data.group) {
    let stop = false
    game.funcs.forEach(e => {
      if (e.group == props.data.group && e.active) {
        stop = true
        return void window.$message.error(`[ ${e.name} ]运行中,请先关闭.`);
      }
    })
    if (stop) return
  }

  // 进入加载状态
  const loading = () => {
    props.data.loading = true
    // .5秒的防抖
    setTimeout(() => {
      props.data.loading = false
    }, 500)
  }
  // 切换激活状态
  const switchAct = () => {
    if (props.data.act) {
      props.data.active = !props.data.active
    }
  }
  // call js代码
  const logic = (dlg?: any) => {
    try {
      dlg && dlg.destroy();
      loading();
      eval(props.data.code)
      switchAct()
    } catch (e) {
    }
  }

  // 一键副本
  if (props.data.name == "一键副本") {
    if (props.data.active) {
      props.data.active = false;
      return window.f_autoMission(true);
    }
    return showDailyTask(switchAct)
  }

  if (props.data.name == "random") {
    return showCustomerSelect()
  }

  if (props.data.act) {
    if (props.data.active) {
      logic();
      return
    }
  }

  if (props.data.tip) {
    const dlg = window.$dialog.info({
      title: props.data.name,
      closable: false,
      content: () => h("span", {
        style: {
          width: "150px",
          backgroundColor: "transparent",
          color: 'white'
        },
      }, { default: () => props.data.tip }),
      action: () => h(ButtonGroup, {
        action: [
          { label: "确定", type: "success", callback: () => logic(dlg) },
          { label: "关闭", type: "default", callback: () => dlg.destroy() },
        ]
      })
    })
    return
  }

  logic();
}
const createDebounce = () => {
  for (let i = 0; i < 40; i++) {
    window["f_debounce" + i] = ref(false)
  }
}

const baseActions = ref<any[]>([])
const createBaseActions = () => {
  baseActions.value = []
  baseActions.value.push(...[
    h(NButton, {
      strong: true,
      secondary: true,
      size: 'tiny',
      onClick: () => goBackCity()
    }, { default: () => "我的城市" }),
    h(NButton, {
      strong: true,
      secondary: true,
      size: 'tiny',
      onClick: () => doMapFly("_mapCtrl.xygz")
    }, { default: () => "新月古镇" }),
    h(NButton, {
      strong: true,
      secondary: true,
      size: 'tiny',
      onClick: () => doMapFly("_mapCtrl.xygzgc")
    }, { default: () => "古镇广场" }),
    h(NButton, {
      strong: true,
      secondary: true,
      size: 'tiny',
      onClick: () => eval("mapFly(_mapCtrl.xygzgc);setTimeout(()=>{xworld.doJumpMap(960,10,20)},1000);")
    }, { default: () => "奇琴伊察" }),
    h(NButton, {
      strong: true,
      secondary: true,
      size: 'tiny',
      onClick: () => doMapFly("_mapCtrl.zjc")
    }, { default: () => "紫禁城" }),
    h(NButton, {
      strong: true,
      secondary: true,
      size: 'tiny',
      onClick: () => doMapFly("_mapCtrl.sy")
    }, { default: () => "圣域" }),
    h(NButton, {
      strong: true,
      secondary: true,
      size: 'tiny',
      onClick: () => doMapFly("_mapCtrl.zysj")
    }, { default: () => "紫云山" }),
  ])
}

// 地图工具
const showMapTool = () => {
  let mapConfigs = eval("_world.traverseMapData()")
  if (!mapConfigs) return
  createDebounce()
  createBaseActions()
  const action = ref<any>([])
  action.value.push(...baseActions.value)
  for (let mapConfig of mapConfigs) {
    const { _name, _id, _x, _y } = mapConfig
    action.value.push(h(NButton, {
      strong: true,
      secondary: true,
      size: 'tiny',
      loading: false,
      onClick: () => callJumpMap(mapConfig)
    }, { default: () => _name || "未知地图" + _id }))
  }

  for (let i = 0; i < action.value.length; i++) {
    const act = action.value[i]
    const click = act.props.onClick
    act.props.onClick = () => {
      action.value[i].props.loading = true;
      action.value = [...new Set(action.value)]
      setTimeout(() => {
        action.value[i].props.loading = false;
        action.value = [...new Set(action.value)]
      }, 500)
      click()
    }
  }

  const dlg = window.$dialog.info({
    title: props.data.name,
    closable: false,
    content: () => h(NSpace, {
      style: {
        width: "80%",
        backgroundColor: "transparent",
      },
    }, {
      default: () => action.value
    }),
    action: () => h(ButtonGroup, {
      action: [
        { label: "关闭", type: "default", callback: () => dlg.destroy() },
      ]
    })
  })
}

const doMapFly = (data: string) => {
  eval(`mapFly(${data})`)
}

const goBackCity = () => {
  eval("City.doEnterCity(xself.getId());")
}

const callJumpMap = (data: { _name: string, _id: number, _x: number, _y: number }) => {
  eval(`xworld.doJumpMap(${data._id}, ${data._x}, ${data._y})`)
}
const gem_callback = ref<null | (() => void)>(null)
const showGemTool = () => {
  const dlg = window.$dialog.info({
    title: props.data.name,
    maskClosable: false,
    closeOnEsc: false,
    closable: false,
    content: () => h(AutoDoGem, { callback: gem_callback }),
    action: () => h(ButtonGroup, {
      action: [
        { label: "开始", type: "success", callback: () => doGemStart() },
        { label: "关闭", type: "default", callback: () => dlg.destroy() },
      ]
    })
  })
}
const identify_callback = ref<null | (() => void)>(null)
const identify_plan_save_callback = ref<null | (() => void)>(null)
const identify_plan_import_callback = ref<null | (() => void)>(null)
const identify_stop_callback = ref<null | (() => void)>(null)
const showIdentifyTool = () => {
  const ok = eval(`!!PanelManager.getPanel(ForgeScene, !1)`)
    && eval(`!!PanelManager.getPanel(ForgeScene, !1).identifyPanel`)
    && eval(`!!PanelManager.getPanel(ForgeScene, !1).identifyPanel.stage`)
  if (!ok) {
    return void window.$message.error("请先进入装备鉴定面板!")
  }
  if (!eval(`!!PanelManager.getPanel(ForgeScene, !1).identifyPanel.item._itemData`)) {
    return void window.$message.error("请选择要鉴定的装备!")
  }
  const dlg = window.$dialog.info({
    title: props.data.name,
    maskClosable: false,
    closeOnEsc: false,
    closable: false,
    content: () => h(AutoIdentify, {
      identify_callback,
      identify_plan_save_callback,
      identify_plan_import_callback,
      identify_stop_callback
    }),
    action: () => h(ButtonGroup, {
      action: [
        {
          label: "保存方案",
          type: "warning",
          callback: () => identify_plan_save_callback.value && identify_plan_save_callback.value()
        },
        {
          label: "导出所有方案",
          type: "info",
          callback: (e) => {
            const plan_data_str = localStorage.getItem("identify_plan_data") || ""
            if (!plan_data_str) return void window.$message.error("没有任何方案数据!")
            window.copy(window.base64Encode(plan_data_str), e.target)
          }
        },
        {
          label: "导入方案",
          type: "error",
          callback: () => identify_plan_import_callback.value && identify_plan_import_callback.value()
        },
        { label: "开始鉴定", type: "success", callback: () => identify_callback.value && identify_callback.value() },
        { label: "关闭", type: "default", callback: () => dlg.destroy() },
      ]
    })
  })
}

const doGemStart = () => {
  gem_callback.value && (gem_callback.value())
}
const use_item_callback = ref<null | (() => boolean)>(null)
const use_item_stopCallback = ref<null | ((code: number) => void)>(null)
const use_item_start_btn_disable = ref(false)
const use_item_stop_btn_disable = ref(false)
const showUseItemTool = () => {
  use_item_stopCallback.value = onUseItemStop
  const dlg = window.$dialog.info({
    title: props.data.name,
    maskClosable: false,
    closable: false,
    content: () => h(AutoUseItem, { callback: use_item_callback, stopCallBack: use_item_stopCallback }),
    action: () => h(ButtonGroup, {
      action: [
        {
          label: "开始使用",
          type: "success",
          disable: use_item_start_btn_disable.value,
          callback: () => doUseItemStart()
        },
        {
          label: "一键开日常盒子", type: "success", disable: use_item_start_btn_disable.value, callback: () => {
            use_item_start_btn_disable.value = true;
            window._world.OpenRewardBox(() => {
              use_item_start_btn_disable.value = false;
            })
          }
        },
        { label: "关闭", type: "default", disable: use_item_stop_btn_disable.value, callback: () => dlg.destroy() },
      ]
    })
  })
}

const doUseItemStart = (dlg?: any) => {
  if (use_item_callback.value) {
    use_item_start_btn_disable.value = true
    use_item_stop_btn_disable.value = true
    use_item_callback.value() && dlg && dlg.destroy()
  }
}

const onUseItemStop = (code: number) => {
  if (code == 1) {
    window.$message.error("万能钥匙不足，停止使用道具", { duration: 6000 })
  }
  if (code == 2) {
    window.$message.error("神秘钥匙不足，停止使用道具", { duration: 6000 })
  }
  use_item_start_btn_disable.value = false
  use_item_stop_btn_disable.value = false
}

const showClearBagArg = () => {
  const call = ref<null | ((type: number, $el?: any) => void)>(null)
  const dlg = window.$dialog.info({
    title: "",
    showIcon: false,
    maskClosable: false,
    closable: false,
    content: () => h(ClearBagArg, { call }),
    action: () => h(ButtonGroup, {
      action: [
        { label: "设置清理参数", type: "success", callback: () => call.value && call.value(1) },
        { label: "移除清理参数", type: "error", callback: () => call.value && call.value(2) },
        { label: "复制清理参数", type: "warning", callback: (e) => call.value && call.value(3, e.target) },
        { label: "关闭", type: "default", callback: () => dlg.destroy() },
      ]
    })
  })
}

// 斩首行动
Evt.on("EVT_KILL_BOSS", Handler.alloc(null, () => {
  const call = ref<null | (() => void)>(null)
  const dlg = window.$dialog.info({
    title: props.data.name,
    maskClosable: false,
    closable: false,
    content: () => h(KillBoss, { call }),
    action: () => h(ButtonGroup, {
      action: [
        {
          label: "击杀",
          type: "error",
          callback: () => call.value && call.value()
        },
        { label: "关闭", type: "default", callback: () => dlg.destroy() },
      ]
    })
  })
}), true)

const parseGameUrl = () => {
  let url = location.href
  let ss = url.indexOf("?")
  url = url.substring(ss)
  return "https://worldh5.gamehz.cn/version/world/publish/channel/res/index.html" + url
}

const dailyTaskCallback = ref<() => any[]>()
const showDailyTask = (onStart: () => void) => {
  // 如果身上有任务，就不让开启
  if (window.xself['missionList'] && window.xself['missionList'].filter((m: any) => m).length) {
    return void window.$message.error("需要先放弃身上的所有任务", { duration: 6000 });
  }
  if (eval(`xself.isInTeam() && xself.isMember()`)) {
    window.$message.warning("队员无需使用", { duration: 10000 });
  }
  window.$message.info("尽量在个人城市开启，并未对每个地图做寻路数据。", { duration: 20000 });

  injectOneKeyTaskTip()
  const dlg = window.$dialog.info({
    title: props.data.name,
    closable: false,
    content: () => h(DailyTaskSelect, { callback: dailyTaskCallback }),
    action: () => h(ButtonGroup, {
      action: [
        { label: "开始", type: "success", callback: () => doAutoDailyMission(onStart, dlg) },
        { label: "关闭", type: "default", callback: () => dlg.destroy() },
      ]
    })
  })
}

const doAutoDailyMission = (onStart: () => void, dlg: any) => {
  if (dailyTaskCallback.value) {
    const data = dailyTaskCallback.value()
    const judge = data.filter(d => d.check);
    let sum = 0
    judge.forEach(j => sum += j.book)
    if (eval("xself.bag.getItemNumByID(40002)") < sum) {
      return void window.$message.error("指令书不够!")
    }
    if (eval("xself.bag.getItemNumByID(40008)") < judge.length) {
      //return void window.$message.error(`为每个副本至少准备一个野外修理卷,需要${judge.length}个`)
      window.$message.warning("修理卷不足，装备烂了就麻烦了。", { duration: 20000 });
    }

    if (judge.length) {
      onStart && onStart()
      window.$daily = judge;
      window.showTipDailyMission(2);
      eval(`oneKeyTask.start()`)
    } else {
      return void window.$message.error("没有选择有效的副本!")
    }
    dlg && dlg.destroy()
  }
}

const injectOneKeyTaskTip = () => {
  // 0 :完成
  // 1 :背包满了
  // 2 :空屏
  window.showTipDailyMission = (code: number) => {
    window.showTip(h(FullScreenTip, {}, {
      content: () => h("div", { style: { display: 'flex', alignItems: 'center', flexDirection: 'column' } }, {
        default: () => [
          h('span', {
            style: {
              color: 'white',
              fontWeight: 'bolder',
              fontSize: '30px',
              transition: 'opacity .1s',
              animationName: 'water',
              animationDuration: '1s',
              animationTimingFunction: 'linear',
              animationIterationCount: 'infinite',
              animationDirection: 'alternate'
            }
          }, { default: () => "正在执行日常任务..." }),
          code == 1 ? h('span', {
            style: {
              color: 'red',
              fontWeight: 'bolder',
              fontSize: '30px'
            }
          }, { default: () => "背包满了!" }) :
            code == 0 ? h('span', {
              style: {
                color: 'green',
                fontWeight: 'bolder',
                fontSize: '30px'
              }
            }, { default: () => "已全部完成." }) : "",
          h(NButton, {
            type: 'success',
            size: 'tiny',
            onClick: () => {
              eval(`oneKeyTask.stop()`)
              window.$daily = null as unknown as any[];
              props.data.active = false;
              window.hideTip();
            }
          }, { default: () => h('span', { style: { color: 'white' } }, { default: () => "停止" }) })
        ]
      })
    }))
  }
}

const showCustomerSelect = (auto: boolean = false) => {
  const dlg = window.$dialog.info({
    title: props.data.name,
    closable: false,
    content: () => h(CustomerSelect, {auto}),
    action: () => h(ButtonGroup, {
      action: [
        {label: "关闭", type: "default", callback: () => dlg.destroy()},
      ]
    })
  })
}

</script>
<style scoped>
:root {
  --btn-color: #fff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

button {
  color: white !important;
  letter-spacing: 1px;
  padding-left: 2px;
  padding-right: 2px;
  border: 1px solid rgba(255, 255, 255, 0) !important;
}

.c-b-s {
  /*zoom: var(--function-btn-zoom, 1);*/
}

/*button {*/
/*  height: 28px;*/
/*  width: 80px;*/
/*  border-radius: 6px;*/
/*  font-weight: bolder;*/
/*  border: 1px solid rgba(255, 255, 255, 0.3);*/
/*  outline: none;*/
/*  background-color: transparent;*/
/*  color: var(--btn-color);*/
/*  font-size: 14px;*/
/*  letter-spacing: 1px;*/
/*  position: relative;*/
/*  transition: .5s;*/
/*  cursor: pointer;*/
/*}*/

button:hover {
  border: 1px solid rgba(255, 255, 255, 0.9) !important;
}

/*button:active {*/
/*  font-size: 10px;*/
/*}*/

.active {
  border-color: rgba(46, 213, 115, 0.9) !important;
  color: rgba(46, 213, 115, 0.9) !important;
}

button.loading::after {
  content: "";
  position: absolute;
  left: -1px;
  top: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: inherit;
}

button.loading::before {
  content: "";
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-left: 2px;
  color: #fff;
  border: 1px solid #fff;
  border-radius: 50%;
  vertical-align: -20%;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 60%);
  animation: loading 2s linear infinite;
}

@keyframes loading {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
